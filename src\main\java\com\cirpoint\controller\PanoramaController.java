package com.cirpoint.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.cirpoint.service.PanoramaService;
import com.cirpoint.util.FileDownloadUtil;
import java.io.File;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 全景图热点编辑控制器
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Slf4j
@RestController
@RequestMapping("/panorama")
public class PanoramaController {

    private final PanoramaService panoramaService;

    @Autowired
    public PanoramaController(PanoramaService panoramaService) {
        this.panoramaService = panoramaService;
    }

    /**
     * 创建新任务
     *
     * @param taskName    任务名称
     * @param modelId     型号ID
     * @param modelName   型号名称
     * @param description 任务描述
     * @return 创建结果
     */
    @PostMapping("/task/create")
    public ResponseEntity<JSONObject> createTask(
            @RequestParam("taskName") String taskName,
            @RequestParam(value = "modelId", required = false) String modelId,
            @RequestParam(value = "modelName", required = false) String modelName,
            @RequestParam(value = "description", required = false) String description) {
        
        try {
            JSONObject result = panoramaService.createTask(taskName, modelId, modelName, description);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("创建任务失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "创建任务失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务列表
     *
     * @return 任务列表
     */
    @GetMapping("/task/list")
    public ResponseEntity<JSONObject> getTaskList() {
        try {
            JSONObject result = panoramaService.getTaskList();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/task/{taskId}")
    public ResponseEntity<JSONObject> getTaskDetail(@PathVariable("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getTaskDetail(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务详情失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取任务详情失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 检查任务是否已存在热点数据
     *
     * @param taskId 任务ID
     * @return 检查结果
     */
    @GetMapping("/check/hotspots")
    public ResponseEntity<JSONObject> checkExistingHotspots(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.checkExistingHotspots(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查热点数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "检查热点数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 清理任务的热点数据和文件信息
     *
     * @param taskId 任务ID
     * @return 清理结果
     */
    @PostMapping("/clear/data")
    public ResponseEntity<JSONObject> clearTaskData(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.clearTaskData(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理任务数据失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "清理任务数据失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传全景图ZIP包
     *
     * @param taskId 任务ID
     * @param file   ZIP文件
     * @return 上传结果
     */
    @PostMapping("/upload/zip")
    public ResponseEntity<JSONObject> uploadPanoramaZip(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadPanoramaZip(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传ZIP文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传ZIP文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 上传单机信息Excel文件
     *
     * @param taskId 任务ID
     * @param file   Excel文件
     * @return 上传结果
     */
    @PostMapping("/upload/excel")
    public ResponseEntity<JSONObject> uploadDeviceExcel(
            @RequestParam("taskId") Long taskId,
            @RequestParam("file") MultipartFile file) {
        
        try {
            JSONObject result = panoramaService.uploadDeviceExcel(taskId, file);
            return ResponseEntity.ok(result);
        } catch (IOException e) {
            log.error("上传Excel文件失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "上传Excel文件失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务下的设备列表
     *
     * @param taskId 任务ID
     * @param page   页码
     * @param limit  每页数量
     * @return 设备列表
     */
    @GetMapping("/device/list")
    public ResponseEntity<JSONObject> getDeviceList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "limit", defaultValue = "15") int limit) {

        try {
            JSONObject result = panoramaService.getDeviceList(taskId, page, limit);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("code", 1);
            errorResult.set("msg", "获取设备列表失败: " + e.getMessage());
            errorResult.set("count", 0);
            errorResult.set("data", new JSONArray());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 导出设备数据为Excel文件
     *
     * @param taskId 任务ID
     * @return Excel文件下载响应
     */
    @GetMapping("/device/export")
    public ResponseEntity<?> exportDeviceExcel(@RequestParam("taskId") Long taskId) {
        try {
            File excelFile = panoramaService.exportDeviceExcel(taskId);
            if (excelFile != null) {
                return FileDownloadUtil.fileResponseAndDelete(excelFile);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("导出Excel文件失败");
            }
        } catch (Exception e) {
            log.error("导出设备Excel失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取热点列表
     *
     * @param taskId 任务ID
     * @param panoramaId 全景节点ID（可选，用于多节点支持）
     * @param page   页码
     * @param limit  每页数量
     * @return 热点列表
     */
    @GetMapping("/hotspot/list")
    public ResponseEntity<JSONObject> getHotspotList(
            @RequestParam("taskId") Long taskId,
            @RequestParam(value = "panoramaId", required = false) String panoramaId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {

        try {
            JSONObject result = panoramaService.getHotspotList(taskId, panoramaId, page, limit);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取热点列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取热点列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取当前全景节点ID
     *
     * @param taskId 任务ID
     * @return 当前节点信息
     */
    @GetMapping("/current-node")
    public ResponseEntity<JSONObject> getCurrentNode(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getCurrentNode(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取当前节点失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取当前节点失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 获取任务下的所有节点列表
     *
     * @param taskId 任务ID
     * @return 节点列表
     */
    @GetMapping("/nodes/list")
    public ResponseEntity<JSONObject> getNodesList(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getNodesList(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取节点列表失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取节点列表失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 更新热点信息
     *
     * @param hotspotId   热点ID
     * @param editedTitle       编辑后热点标题
     * @param editedDescription 编辑后热点描述
     * @param deviceId    关联单机ID
     * @return 更新结果
     */
    @PostMapping("/hotspot/update")
    public ResponseEntity<JSONObject> updateHotspot(
            @RequestParam("hotspotId") Long hotspotId,
            @RequestParam(value = "editedTitle", required = false) String editedTitle,
            @RequestParam(value = "editedDescription", required = false) String editedDescription,
            @RequestParam(value = "deviceId", required = false) Long deviceId) {
        
        try {
            JSONObject result = panoramaService.updateHotspot(hotspotId, editedTitle, editedDescription, deviceId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新热点信息失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "更新热点信息失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }



    /**
     * 热点定位
     *
     * @param hotspotId 热点ID
     * @return 定位信息
     */
    @PostMapping("/hotspot/locate")
    public ResponseEntity<JSONObject> locateHotspot(@RequestParam("hotspotId") Long hotspotId) {
        try {
            JSONObject result = panoramaService.locateHotspot(hotspotId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("热点定位失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "热点定位失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 导出修改后的全景图包
     *
     * @param taskId 任务ID
     * @return ZIP文件下载响应
     */
    @PostMapping("/export")
    public ResponseEntity<?> exportPanorama(@RequestParam("taskId") Long taskId) {
        try {
            String zipFilePath = panoramaService.exportPanorama(taskId);
            if (zipFilePath != null) {
                return FileDownloadUtil.fileResponseAndDelete(new File(zipFilePath));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("导出全景图包失败");
            }
        } catch (Exception e) {
            log.error("导出全景图包失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取全景图预览路径
     *
     * @param taskId 任务ID
     * @return 预览路径
     */
    @GetMapping("/preview/path")
    public ResponseEntity<JSONObject> getPreviewPath(@RequestParam("taskId") Long taskId) {
        try {
            JSONObject result = panoramaService.getPreviewPath(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取预览路径失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "获取预览路径失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 测试数据库连接和表结构
     *
     * @return 测试结果
     */
    @GetMapping("/test/database")
    public ResponseEntity<JSONObject> testDatabase() {
        try {
            JSONObject result = panoramaService.testDatabase();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("测试数据库失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.set("success", false);
            errorResult.set("msg", "测试数据库失败: " + e.getMessage());
            return ResponseEntity.ok(errorResult);
        }
    }
}
