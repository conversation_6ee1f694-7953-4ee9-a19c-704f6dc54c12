---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## Headers

This document defines the rules and guidelines for the AI coding assistant in this project. It covers coding standards, project-specific conventions, debugging, workflow, and other relevant practices.

## Project Description & Conventions
- The project involves managing data packages, potentially including components for AIT screens and BPM workflows.
- File names and directory structures mentioned in user requests should be adhered to. Pay close attention to files like `nonconformity-chart.js`, `QueryNonconformityList.js`, `create_bpm.sql`, and directories like `twx/Thing.Fn.BPM`.
- When generating code, refer to existing files (e.g., `panorama-editor.html`, `项目开发总结_全景图热点编辑系统.md`) for context and guidance.
- When asked to summarize a file or page, include all relevant file paths and descriptions to guide future AI coding conversations.

## TECH STACK
- JavaScript (ES5 and up)
- Spring Boot
- Java
- HTML
- CSS
- Lombok
- Oracle SQL
- Maven
- Bootstrap
- LayUI
- jQuery 3.x
- Apache Commons
- Hutool

## Coding Standards

### JavaScript
- Follow established JavaScript conventions.
- Ensure code is compatible with ES5 unless explicitly instructed otherwise.
- When modifying existing functions, maintain their original structure and purpose.
- **ES5 Syntax Requirements:**
  - Use `var` for variable declarations.
  - Use `response.data || []` for handling potentially undefined data.
  - Use standard string concatenation instead of template literals.
  - Avoid ES6+ features like `const`, `let`, arrow functions, destructuring, and `Promise/async/await`.

### Java
- Adhere to standard Java coding conventions.
- Use Lombok annotations (e.g., `@Slf4j`) for logging where appropriate.

### SQL
- Write standard Oracle SQL.
- When creating or modifying SQL scripts, include comments to explain the purpose and logic.
- Ensure SQL scripts handle potential errors and edge cases gracefully.

### General
- Use clear and descriptive names for variables, functions, and classes.
- Comment code adequately to explain its functionality.
- Handle potential errors gracefully with appropriate error messages.
- Follow KISS (Keep It Simple, Stupid) and YAGNI (You Ain't Gonna Need It) principles.
- Prioritize code readability.
- Each function/method should have a single responsibility.

## User Interface (UI) Guidelines

### General
- Aim for clean, professional designs suitable for office environments.
- Prioritize clarity and readability.
- Adhere to established UI patterns and conventions.
- For responsive designs, consider resolutions 1920x1080 and 1366x768.

### CSS
- Utilize a consistent CSS design system with tokens for colors, fonts, spacing, and other style attributes.
- Ensure responsiveness across different screen sizes (desktop, tablet, mobile).
- Optimize CSS for performance (e.g., minimize file sizes, use hardware acceleration).
- Be mindful of accessibility (e.g., high contrast, keyboard navigation).
- Use Bootstrap grid system for layout.
- For custom styles, use BEM naming convention.

### Specific UI Elements
- **Tables:** Use clear borders and appropriate spacing for readability.
- **Buttons:** Provide clear visual feedback on hover and click.
- **Forms:** Align labels and inputs properly.

## Workflow & Release Rules

- Follow a well-defined workflow for development, testing, and deployment.
- Implement a code review process to ensure quality and adherence to standards.
- Use a version control system (e.g., Git) to manage code changes.
- Create a detailed README file for each service or component, explaining its purpose, usage, and configuration.
- If introducing a new library, make sure to check its license is compatible with the project.
- Before releasing a new version, back up the current version.
- Database script changes require a rollback plan.
- Use gradual rollout for production environment deployments.

## DEBUGGING

- Implement comprehensive logging using `@Slf4j` in Java and `console.log` or similar in JavaScript.
- Include informative messages to track the flow of execution and potential errors.
- Use try-catch blocks to handle exceptions gracefully.
- Monitor application performance and resource usage to identify bottlenecks.
- **Front-end debugging:** Use browser developer tools and check `console.log` outputs.
- **Back-end debugging:** View ThingWorx platform logs.
- **JavaScript Debugging Removal:** All `console.log` and testing `layer.msg` statements must be removed from JavaScript files before deployment.

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

- All code modifications must be accompanied by clear and concise documentation.
- Create detailed architecture diagrams to illustrate system components and their interactions.
- Maintain a consistent documentation style throughout the project.
- Create tasks file to record project's progress.
- Important function modifications should be recorded in `project_document/logs/`.
- SQL script changes need to be archived in the respective developer's directory.
- Update README before version release.
- The `ai-docs/` directory should be used for project documentation.
- The project root directory should contain the `ai-docs/architecture/` directory structure.
- **Panorama Hotspot Editor Analysis Reports:** Store detailed analysis reports for the Panorama Hotspot Editor page in `ai-docs/panorama-editor-report.md`. These reports should include file paths, file descriptions, and other relevant information to guide future AI coding conversations.
- **Panorama Hotspot Editor Documentation Update:** After reviewing the latest code for `panorama-editor.html`, update the `ai-docs/panorama-editor-report.md` document to reflect any changes or new features.

## Data Handling

- When working with data, ensure data integrity and consistency.
- Validate input data to prevent errors and security vulnerabilities.
- Handle different data types appropriately.
- When displaying data in the UI, format it clearly and consistently.
- Always handle null or undefined values gracefully.

## ERROR HANDLING

- Implement robust error handling throughout the application.
- Use try-catch blocks to catch potential exceptions.
- Log errors with informative messages.
- Provide user-friendly error messages in the UI.

## Security
- Be aware of potential security vulnerabilities, such as SQL injection and cross-site scripting (XSS).
- Implement appropriate security measures to protect against these vulnerabilities.
- Validate user input to prevent malicious code from being injected into the system.
- **SQL Injection Prevention:** Use parameterized queries.
- **XSS Prevention:** Escape user input data.
- **Permissions Control:** Verify user operation permissions.
- **Data Backup:** Regularly back up important data.

## Naming Conventions

- Follow consistent naming conventions for files, classes, variables, and functions.
- Use kebab-case for file names.

## DataPackageManagement Specific Rules

### Nonconformity Review Formatting
- When displaying nonconformity review details, strictly adhere to the format specified in the provided images or mockups.
- Ensure all relevant fields from the data source are included in the display.
- Design the layout to resemble a traditional paper form for a professional and familiar look.
- Consider using a table-based layout with borders and shading to achieve the desired effect.

### Branch Data Simulation Service
- Create a dedicated service for simulating branch data.
- Ensure all fields in the branch table have valid values.
- Maintain data consistency between the main and branch tables, especially for key fields like model and development phase.
- Provide options for generating different numbers of branch records per main record.
- The branch FZ filed should follow the format "FZ-BINDID-001".

### ThingWorx Specific Rules
- When modifying ThingWorx JavaScript services, adhere to ThingWorx coding standards.
- Use the standard `result` object for returning data from ThingWorx services.
- Handle InfoTables correctly, extracting data as needed.
- Services should be a single function, not composed of multiple functions.
- Throw errors instead of returning them.
- When working with services in ThingWorx, ensure that proper permissions are set for the Entities.
- **ThingWorx Service Template:**
```javascript
/**
 * @definition    GetDataList
 * @description   获取数据列表    wanghq 2025年6月3日14:38:01
 * @implementation    {Script}
 *
 * @param    {STRING}    searchKey    搜索关键字
 * @param    {INTEGER}    pageNum    页码
 * @param    {INTEGER}    pageSize    每页大小
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 参数验证
    if (!pageNum || pageNum < 1) pageNum = 1;
    if (!pageSize || pageSize < 1) pageSize = 20;

    // 构建SQL查询
    var sql = "SELECT * FROM data_table WHERE 1=1";
    if (searchKey && searchKey.trim() !== '') {
        sql += " AND name LIKE '%" + searchKey + "%'";
    }

    // 分页查询 (Oracle 11g ROWNUM方式)
    var startRow = (pageNum - 1) * pageSize + 1;
    var endRow = pageNum * pageSize;
    var pagingSql = "SELECT * FROM (" +
        "SELECT ROWNUM AS rn, t.* FROM (" + sql + ") t " +
        "WHERE ROWNUM <= " + endRow + ") " +
        "WHERE rn >= " + startRow;

    // 执行查询
    var jsonData = Things['Thing.DB.Oracle'].RunQuery({sql: pagingSql}).ToJSON().rows;

    // 获取总数
    var countSql = "SELECT COUNT(*) as total FROM (" + sql + ")";
    var countResult = Things['Thing.DB.Oracle'].RunQuery({sql: countSql}).ToJSON().rows;
    var total = countResult.length > 0 ? countResult[0].total : 0;

    res.success = true;
    res.data = {
        list: jsonData || [],
        total: total,
        pageNum: pageNum,
        pageSize: pageSize
    };
    res.msg = "查询成功";

} catch (error) {
    res.success = false;
    var msg = "GetDataList-查询失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;
```

## Version Control
- Always use version control (e.g., Git) to track changes to the codebase.
- Create meaningful commit messages to explain the purpose of each change.
- Use branches to isolate new features or bug fixes.
- Follow a consistent branching strategy.

## Testing
- Write unit tests to verify the correctness of individual components.
- Perform integration tests to ensure that different parts of the system work together properly.
- Conduct user acceptance testing (UAT) to validate that the system meets the needs of the users.
- **Unit Test Example (JavaScript):**
```javascript
function testFunction() {
    // 测试数据准备
    var testData = {name: '测试用户', age: 25};

    // 执行被测试函数
    var result = processUserData(testData);

    // 断言验证
    console.assert(result.success === true, '函数应该返回成功状态');
    console.assert(result.data.name === '测试用户', '用户名应该正确处理');
}
```

## Cursor Rules
- The project now includes cursor rules located in `.cursor/rules/`.
- The rules consist of two main files: `data-package-management-architecture.mdc` and `development-best-practices.mdc`.
- `data-package-management-architecture.mdc`: Contains system architecture navigation and business process descriptions.
- `development-best-practices.mdc`: Contains development best practices and coding standards, including ES5 syntax, ThingWorx service development standards, and Oracle database operation specifications.

## Panorama Hotspot Editor Specific Rules

### Project Overview
- **Project Name:** Panorama Hotspot Editor
- **Tech Stack:** Spring Boot 2.7.18 + JDK 1.8 + Layui 2.10.3 + jQuery 3.x + Oracle 11g
- **Project Type:** Web application
- **Development Status:** Production-ready
- **Last Updated:** 2025-06-08

### File Structure
- **Frontend:**
  - `FileHandle/src/main/webapp/panorama-editor.html`: Main HTML page (272 lines)
  - `FileHandle/src/main/webapp/panorama/css/panorama-editor.css`: CSS Styles (810 lines)
  - `FileHandle/src/main/webapp/panorama/js/panorama-editor.js`: Core JavaScript logic (1334 lines)
  - `FileHandle/src/main/webapp/panorama/js/pano2vr-hotspot-locator.js`: Hotspot locator script (112 lines)
- **Backend:**
  - `FileHandle/src/main/java/com/cirpoint/controller/PanoramaController.java`: REST API controller (401 lines)
  - `FileHandle/src/main/java/com/cirpoint/service/PanoramaService.java`: Business logic service (1057 lines)
  - `FileHandle/src/main/java/com/cirpoint/util/Pano2VRHotspotInjector.java`: Script injection utility (224 lines)
- **Database:**
  - `DataPackageManagement/sql/panorama_tables.sql`: Database table definitions (190 lines)

### API Endpoints
- `POST /panorama/task/create`: Create task
- `GET /panorama/task/list`: Get task list
- `GET /panorama/task/{taskId}`: Get task details
- `GET /panorama/check/hotspots`: Check hotspot data
- `POST /panorama/clear/data`: Clear task data
- `POST /panorama/upload/zip`: Upload panorama ZIP
- `POST /panorama/upload/excel`: Upload device Excel
- `GET /panorama/device/list`: Get device list
- `GET /panorama/device/export`: Export device Excel
- `GET /panorama/hotspot/list`: Get hotspot list
- `POST /panorama/hotspot/update`: Update hotspot information
- `POST /panorama/hotspot/locate`: Hotspot location data
- `POST /panorama/export`: Export panorama
- `GET /panorama/preview/path`: Get preview path

### System Architecture
```
Frontend (Layui + jQuery)
↓ HTTP/AJAX
Controller (Spring Boot REST API)
↓ Method Call
Service Layer (Business Logic)
↓ Branching
File System | Database | Utilities
```

### Core Technologies
- **Frontend:** Layui 2.10.3, jQuery 3.x, CSS3, HTML5, jQuery FileDownload plugin
- **Backend:** Spring Boot 2.7.18, JDK 1.8, Hutool, Dom4j, Apache Commons
- **Database:** Oracle 11g

### Development Setup
- **JDK:** 1.8 or higher
- **IDE:** Eclipse/IntelliJ IDEA
- **Build Tool:** Maven
- **Database:** Oracle 11g or higher
- **Web Server:** Tomcat 8.5 or higher

### Database Initialization
1.  Connect to Oracle: `sqlplus username/password@database`
2.  Run script: `@DataPackageManagement/sql/panorama_tables.sql`

### Configuration
- **application.properties:**
```properties
spring.datasource.url=*************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
```
- **File Upload Path:** Defined in `ApplicationConfig.java`

### Directory Structure
```
fileUploadPath/
└── panorama/
    └── {taskId}/
        ├── panorama_timestamp.zip
        └── extracted/
            ├── *.html
            ├── *.xml
            └── images/
```

### Debugging
- **Frontend:**
```javascript
var DEBUG_MODE = true;
if (DEBUG_MODE) {
    console.log('Debug Info:', data);
}
```
- **Backend:**
```java
@Slf4j
public class PanoramaService {
    public void someMethod() {
        log.info("Method started");
        log.debug("Data: {}", data);
        log.error("Error", exception);
    }
}
```

### Deployment
- **Files:**
  - `FileHandle.war`
  - `DataPackageManagement/sql/panorama_tables.sql`
  - `application-prod.properties`
  - `logback-spring.xml`
- **application-prod.properties (Example):**
```properties
server.port=8080
spring.datasource.url=***********************************
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
file.upload.path=/opt/panorama/uploads
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
logging.level.com.cirpoint=INFO
logging.file.path=/opt/panorama/logs
```

### Troubleshooting
- **File Upload Issues:** Check file size limits, ZIP format, directory permissions, and backend logs.
- **Hotspot Location Issues:** Check iframe loading, script injection, postMessage communication, and PAN/TILT data.
- **Table Display Issues:** Check database connection, API data format, Layui initialization, and JavaScript errors.

### Version History
- **v1.0.0 (2025-06-08):** Updated project documentation to reflect latest code structure.

### Panorama Hotspot Editor Detailed Summary Report

- This section provides a detailed summary of the Panorama Hotspot Editor, generated on 2025-06-08, based on a review of the code files and the document `项目开发总结_全景图热点编辑系统.md`. This summary is intended to guide future AI coding conversations.

#### 1. System Overview

-   The Panorama Hotspot Editor is a full-featured web application designed to provide an efficient and intuitive platform for managing panorama projects, editing hotspot information in images, and associating it with external device data. The system supports a complete workflow from task creation to file uploading, hotspot editing, real-time preview, hotspot location, and final result export.
-   The system adopts a modern design concept, providing a friendly user interface and smooth operation experience, especially in task guidance, data integrity checking, and hotspot location.

#### 2. Core Technology Stack
-   **Database:** Oracle 11g
-   **Panorama Engine:** Pano2VR (integrated via iframe)
-   **File Processing:** Apache Commons, Hutool tool libraries
-   **Frontend Enhancement:** jQuery FileDownload plugin

#### 3. Associated File List and Analysis

##### 3.1. Frontend Files

| File Path                                                                 | File Type | Core Responsibilities                                                                                                                                                                                                                                                           |
| ------------------------------------------------------------------------ | -------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `FileHandle/src/main/webapp/panorama-editor.html`                        | HTML     | **System Main Framework**. Adopts a responsive layout design, including a top navigation bar (task selection, creation, export), left and right column structure (task information, hotspot editing table, panorama preview), task guidance mask, and various dialog boxes (create task, edit hotspot). |
| `FileHandle/src/main/webapp/panorama/css/panorama-editor.css`            | CSS      | **Visual Styles and Interaction Effects**. Implements a modern interface design, including gradient backgrounds, dark theme adaptation, draggable separator styles, task selection guide mask animation, deep customization of Layui components, and complete responsive layout support. |
| `FileHandle/src/main/webapp/panorama/js/panorama-editor.js`              | JS       | **Page Main Logic Controller** (1484 lines). Responsible for complete front-end interaction logic, including Layui component initialization, task management processes, file upload handling, hotspot editing functions, preview management, node switching, data integrity checking, and comprehensive communication with the back-end API. |
| `FileHandle/src/main/webapp/panorama/js/pano2vr-hotspot-locator.js`      | JS       | **Hotspot Locator Script**. A dedicated script for the hotspot location function, dynamically injected into the HTML generated by Pano2VR. It receives `postMessage` instructions from the main page and calls the Pano2VR API to achieve precise hotspot location.                                  |
| `FileHandle/src/main/webapp/static/lib/jquery/jquery.fileDownload.js`    | JS       | **File Download Enhancement Plugin**. Provides a more reliable file download experience than native `window.open`, supporting file stream processing, download progress feedback, error handling, and other advanced features.                                                                  |

##### 3.2. Backend Files

| File Path                                                              | File Type | Core Responsibilities                                                                                                                                                                                                                                                          |
| ------------------------------------------------------------------- | -------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `FileHandle/src/main/java/com/cirpoint/controller/PanoramaController.java` | Java     | **RESTful API Controller** (402 lines). Defines a complete API interface specification, including 15 core interfaces for task management (creation, query, details), file uploading (ZIP, Excel), hotspot operations (query, update, location), data checking and cleaning, preview path acquisition, and export functions. |
| `FileHandle/src/main/java/com/cirpoint/service/PanoramaService.java`       | Java     | **Core Business Logic Layer**. Implements the specific logic for all business functions, including database CRUD operations, file processing (ZIP decompression/packaging, Excel parsing/generation), script injection management, preview path processing, and complex business process control.                                  |
| `FileHandle/src/main/java/com/cirpoint/util/Pano2VRHotspotInjector.java`     | Java     | **Script Injection/Cleaning Tool Class**. Specifically responsible for automatically injecting the hotspot location script into HTML files during ZIP package processing, and cleaning the script during export to ensure the purity of the delivered files and the continuity of online editing functions.                                 |

##### 3.3. Database Table Structure

| Table Name          | Core Responsibilities                                        | Key Fields                                    |
| ------------------- | --------------------------------------------------------------- | ------------------------------------------ |
| `PANORAMA_TASK`     | Stores basic task information and status management.                    | TASK_ID, TASK_NAME, MODEL_ID, STATUS, etc.     |
| `PANORAMA_HOTSPOT`  | Stores detailed hotspot information and editing status for each task.           | HOTSPOT_ID, TASK_ID, PAN, TILT, EDITED, etc.   |
| `PANORAMA_DEVICE`   | Stores single-machine/device information associated with the task.                   | DEVICE_ID, TASK_ID, DEVICE_NAME, etc.          |

---

#### 4. Core Workflows

##### 4.1. Page Initialization and Task Guidance Process

1.  **Page Loading**: User visits `panorama-editor.html`, and the page uses a fixed header layout with the main area height set to `calc(100vh - 60px)`.
2.  **JS Initialization**: `panorama-editor.js` executes three core initialization functions:
    -   `initPage()`: Initializes the hotspot table, file upload component, and drag functionality
    -   `bindEvents()`: Binds all UI event listeners
    -   `loadTaskList()`: Asynchronously loads task list data
3.  **Guidance Mask Display**: `showTaskSelectionMask()` displays a semi-transparent guidance mask with a fade-in animation, guiding the user to select a task.
4.  **Task Selection Process**:
    -   Users can select an existing task via the top dropdown box
    -   Or click the "Create Task" button to create a new task
    -   The system supports a task switching confirmation mechanism to prevent data loss
5.  **Mask Removal**: After a task is selected, `hideTaskSelectionMask()` executes a fade-out animation and enables all function buttons.

##### 4.2. Data Integrity and Cleaning Process

1.  **Pre-Upload Check**: When a user uploads a file, the system first calls `GET /panorama/check/hotspots` to check if data exists.
2.  **Confirmation Dialog**: If existing data is detected, a `layer.confirm` dialog box pops up, clearly informing the user of the consequences of the operation.
3.  **Data Cleaning**: After the user confirms, `POST /panorama/clear/data` is called to clean up related data table records.
4.  **Execute Upload**: The original upload process continues after cleaning is completed, ensuring data consistency.

##### 4.3. Hotspot Location Workflow (System Core Technical Difficulty)

This is one of the most complex functions of the system, involving front-end and back-end collaboration, iframe communication, third-party library integration, and other technical aspects.

###### Preparation Stage (ZIP Upload):

1.  **File Reception**: The backend `PanoramaController.uploadPanoramaZip()` receives the ZIP file
2.  **Decompression Processing**: `PanoramaService` decompresses the ZIP package into the specified directory
3.  **Script Injection**: Calling `Pano2VRHotspotInjector.processDirectory()`:
    -   Traverses all `.html` files
    -   Injects `<script src=".../pano2vr-hotspot-locator.js"></script>` before the `</body>` of each HTML
    -   The injected HTML has the ability to receive location commands

###### Execution Stage (User Clicks Locate):

1.  **Event Trigger**: The user clicks the "Locate" button in the hotspot table
2.  **Frontend Processing**: The `locateHotspot(hotspotId)` function of `panorama-editor.js` is called
3.  **Backend Query**: Sends a request to `POST /panorama/hotspot/locate` to get the `PAN` and `TILT` coordinates of the hotspot
4.  **Iframe Communication**: Sends a location command to the preview window via `panoramaFrame.contentWindow.postMessage()`
5.  **Script Response**: The injected `pano2vr-hotspot-locator.js` listens for the message and calls the `pano.moveTo(pan, tilt)` API
6.  **View Movement**: The panoramic view smoothly moves to the specified coordinate position

###### Cleaning Stage (Export):

1.  **Export Trigger**: The user clicks the export button, triggering `POST /panorama/export`
2.  **Script Cleaning**: Calling `Pano2VRHotspotInjector.cleanDirectory()` uses regular expressions to remove the injected script tags
3.  **File Packaging**: The cleaned pure files are packaged and returned to the user
4.  **Re-injection**: Re-inject the script immediately after the export is complete to ensure that the online editing function is not affected

##### 4.4. Node Switching and Status Management

1.  **Current Node Display**: The system displays the information of the currently edited panorama node
2.  **Node Switching Detection**: Handles node changes through `handleNodeSwitch()`
3.  **Table Refresh**: Automatically refreshes the hotspot table data when the node is switched
4.  **Status Synchronization**: Ensures the consistency of UI status and backend data

#### 5. User Interface Features

##### 5.1. Task Guidance System

-   **Guidance Mask**: Guidance interface with gradient animation effects
-   **Status Prompts**: Clear task status display and operation guidance
-   **Error Prevention**: Confirmation mechanism when switching tasks

##### 5.2. Responsive Layout Design

-   **Draggable Separator**: The width of the left and right panels can be adjusted by dragging
-   **Adaptive Layout**: Supports responsive display on different screen sizes
-   **Modern Style**: Employs modern UI elements such as gradient backgrounds, card-style designs, and soft shadows

##### 5.3. Interactive Experience Optimization

-   **File Drag-and-Drop Upload**: Supports uploading files by dragging and dropping
-   **Real-Time Status Feedback**: Instant feedback on upload progress and operation results
-   **In-Table Editing**: Quick editing and updating of hotspot information

#### 6. Guidance Suggestions for Future AI

##### 6.1. Code Modification Entry Points

-   **Frontend UI/Interaction**: Mainly modify `panorama-editor.html` (structure) and `panorama-editor.js` (logic)
-   **Style Beautification**: Focus on `panorama-editor.css`, paying attention to responsive layout and Layui component customization
-   **Backend Business**: The core logic is in `PanoramaService.java`, and the API interface is in `PanoramaController.java`
-   **Script Injection**: Involves `Pano2VRHotspotInjector.java` and `pano2vr-hotspot-locator.js`

##### 6.2. Key Functions and Areas

-   **`initPage()`**: Unified entry point for page initialization
-   **`loadTaskList()`**: Core function for task list management
-   **`selectTask(taskId)`**: Key logic for task selection and switching
-   **`locateHotspot(hotspotId)`**: Initiator of the hotspot location function
-   **`checkExportConditions()`**: Export condition checking and button status control
-   **`showTaskSelectionMask()`/`hideTaskSelectionMask()`**: Guidance mask management

##### 6.3. Technical Architecture Compliance Principles

-   **API Communication Mode**: Strictly use jQuery AJAX, following the `{success: boolean, msg: string, data: object}` response format
-   **User Interaction Feedback**: Unify using Layui's `layer.msg`, `layer.alert`, `layer.confirm` and other components
-   **File Processing Specifications**: Reuse existing file tool classes to maintain consistency in processing logic
-   **Iframe Communication Mechanism**: Continue to use the `postMessage` mode, and add message types in `pano2vr-hotspot-locator.js` when extending functions

##### 6.4. Special Precautions

-   **Iframe Event Handling**: Operations involving iframes such as dragging require the use of a mask layer (`dragOverlay`) to solve event capture problems
-   **Export Function Atomicity**: When modifying the export function, ensure the atomicity of "cleaning → packaging → re-injection" to avoid damaging online editing
-   **Layui Component Customization**: Deep customization requires careful handling of dynamically generated DOM and CSS overrides
-   **Data Integrity**: All data change operations should include integrity checks and user confirmation mechanisms

##### 6.5. Performance Optimization Suggestions

-   **Lazy Loading of Table Data**: Consider pagination and virtual scrolling for large amounts of hotspot data
-   **File Upload Optimization**: Add progress bars and breakpoint resume for large file uploads
-   **Preview Loading Optimization**: Delay loading and caching strategies for iframe previews
-   **Script Injection Optimization**: Performance optimization when batch processing HTML files

#### 7. Advantages of System Architecture

1.  **Modular Design**: Front-end and back-end separation with clear responsibilities, facilitating maintenance and extension
2.  **User Experience Priority**: Complete guidance process and friendly interactive feedback
3.  **Data Security Guarantee**: Multi-level data integrity checking and confirmation mechanism
4.  **Modern Technology Stack**: Using a mature and stable technology combination to ensure system reliability
5.  **Good Scalability**: Clear architectural design facilitates function iteration and technology upgrades

This report provides a comprehensive technical analysis and development guide for the Panorama Hotspot Editor and is an important reference document for future AI system maintenance and function expansion.