/**
 * 全景图热点编辑系统 - 主要JavaScript文件
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */

layui.use(['layer', 'form', 'table', 'upload', 'element'], function() {
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var upload = layui.upload;
    var element = layui.element;
    var $ = layui.$;

    // 全局变量
    var currentTaskId = null;
    var hotspotTable = null;
    var deviceList = [];

    // 初始化
    $(document).ready(function() {
        initPage();
        bindEvents();
        loadTaskList();
        // 显示任务选择蒙版
        showTaskSelectionMask();
    });

    /**
     * 初始化页面
     */
    function initPage() {
        // 初始化热点表格
        initHotspotTable();

        // 初始化文件上传
        initFileUpload();

        // 初始化拖拽功能
        initResizeHandle();
    }

    /**
     * 绑定事件
     */
    function bindEvents() {
        // 创建任务按钮
        $('#createTaskBtn').on('click', function() {
            showCreateTaskDialog();
        });

        // 导出按钮 - 点击时检查条件并给出明确提示
        $('#exportBtn').off('click').on('click', function() {
            // 检查是否选择了任务
            if (!currentTaskId) {
                layer.msg('请先选择一个任务', {icon: 2});
                return;
            }

            // 检查任务状态和条件
            checkExportConditions();
        });

        // 刷新表格按钮
        $('#refreshTableBtn').on('click', function() {
            if (currentTaskId) {
                hotspotTable.reload();
            }
        });

        // 刷新预览按钮
        $('#refreshPreviewBtn').on('click', function() {
            if (currentTaskId) {
                loadPreview();
            }
        });

        // 任务选择监听
        form.on('select(taskSelect)', function(data) {
            var taskId = data.value;


            if (taskId) {
                // 如果当前已有任务，提示用户确认切换
                if (currentTaskId && currentTaskId != taskId) {
                    layer.confirm('切换任务将清空当前工作状态，请确保已保存所有修改。是否继续切换？', {
                        icon: 3,
                        title: '确认切换任务',
                        btn: ['确认切换', '取消']
                    }, function(index) {
                        // 用户确认切换
                        layer.close(index);
                        selectTask(taskId);
                    }, function(index) {
                        // 用户取消切换，恢复原选择
                        layer.close(index);
                        $('#taskSelect').val(currentTaskId);
                        form.render('select');
                    });
                } else {
                    // 首次选择任务，直接切换
                    selectTask(taskId);
                }
            } else {
                // 选择空值，清空任务信息
                if (currentTaskId) {
                    layer.confirm('确定要取消选择当前任务吗？这将清空所有工作状态。', {
                        icon: 3,
                        title: '确认取消选择',
                        btn: ['确认', '取消']
                    }, function(index) {
                        layer.close(index);
                        clearTaskInfo();
                    }, function(index) {
                        layer.close(index);
                        $('#taskSelect').val(currentTaskId);
                        form.render('select');
                    });
                } else {
                    clearTaskInfo();
                }
            }
        });

        // 节点选择器事件
        form.on('select(nodeSelector)', function(data) {
            var selectedNodeId = data.value;

            if (isNodeSelectorSyncing) {
                return; // 防止循环触发
            }

            if (selectedNodeId && selectedNodeId !== currentNodeId) {
                // 用户手动选择了新节点，需要通知预览窗口切换
                switchToNode(selectedNodeId);
            }
        });

        // 创建任务表单提交 - 注意这里不需要lay-filter，因为我们手动触发
        // form.on('submit(createTask)', function(data) {
        //     createTask(data.field);
        //     return false;
        // });

        // 编辑热点表单提交 - 注意这里不需要lay-filter，因为我们手动触发
        // form.on('submit(editHotspot)', function(data) {
        //     updateHotspot(data.field);
        //     return false;
        // });

        // 初始化导出按钮状态
        updateExportButtonState();
    }

    /**
     * 加载任务列表
     */
    function loadTaskList(callback) {
        $.get('/panorama/task/list', function(res) {
            if (res.success) {
                var options = '<option value="">请选择任务</option>';
                $.each(res.data, function(i, task) {
                    var statusText = getStatusText(task.STATUS);
                    options += '<option value="' + task.TASK_ID + '">' +
                              task.TASK_NAME + ' (' + statusText + ')</option>';
                });
                $('#taskSelect').html(options);
                form.render('select');

                // 如果有回调函数，执行它
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                layer.msg('加载任务列表失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 获取状态文本
     */
    function getStatusText(status) {
        switch(status) {
            case 0: return '创建中';
            case 1: return '已完成';
            case 2: return '已导出';
            default: return '未知';
        }
    }

    /**
     * 格式化时间戳
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '-';

        // 如果是13位时间戳，直接使用；如果是10位，需要乘以1000
        var time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
        var date = new Date(time);

        if (isNaN(date.getTime())) return '-';

        var year = date.getFullYear();
        var month = String(date.getMonth() + 1).padStart(2, '0');
        var day = String(date.getDate()).padStart(2, '0');
        var hours = String(date.getHours()).padStart(2, '0');
        var minutes = String(date.getMinutes()).padStart(2, '0');
        var seconds = String(date.getSeconds()).padStart(2, '0');

        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
    }

    /**
     * 选择任务
     */
    function selectTask(taskId) {
        currentTaskId = taskId;

        // 隐藏任务选择蒙版
        hideTaskSelectionMask();

        // 加载任务详情
        $.get('/panorama/task/' + taskId, function(res) {
            if (res.success) {
                updateTaskInfo(res.data);
                enableUploadButtons();
                loadDeviceList();
                loadNodesList(); // 加载节点列表
                loadHotspotData();
                loadPreview();
                // 导出按钮状态会在updateTaskInfo中通过updateExportButtonState更新
            } else {
                layer.msg('加载任务详情失败: ' + res.msg, {icon: 2});
            }
        });
    }

    /**
     * 更新任务信息显示
     */
    function updateTaskInfo(taskData) {
        $('#taskName').text(taskData.TASK_NAME || '-');

        // 合并型号信息显示
        var modelInfo = '';
        if (taskData.MODEL_ID && taskData.MODEL_NAME) {
            modelInfo = taskData.MODEL_ID + ' - ' + taskData.MODEL_NAME;
        } else if (taskData.MODEL_ID) {
            modelInfo = taskData.MODEL_ID;
        } else if (taskData.MODEL_NAME) {
            modelInfo = taskData.MODEL_NAME;
        } else {
            modelInfo = '-';
        }
        $('#modelInfo').text(modelInfo);

        $('#taskDescription').text(taskData.DESCRIPTION || '-');
        $('#createTime').text(formatTimestamp(taskData.CREATE_TIME));
        $('#taskStatus').text(getStatusText(taskData.STATUS));

        // 更新上传状态
        $('#zipStatus').text(taskData.ZIP_FILE_PATH ? '已上传' : '未上传');
        $('#excelStatus').text('未知'); // 需要额外查询

        // 使用统一的导出按钮管理函数
        updateExportButtonState();
    }

    /**
     * 统一管理导出按钮状态
     */
    function updateExportButtonState() {
        if (!currentTaskId) {
            // 没有选择任务时，隐藏导出按钮
            $('#exportBtn').hide();
        } else {
            // 有任务时，显示按钮（始终可点击）
            $('#exportBtn').show().prop('disabled', false);
        }
    }

    /**
     * 检查导出条件
     */
    function checkExportConditions() {
        // 获取任务详细信息
        $.get('/panorama/task/' + currentTaskId, function(res) {
            if (!res.success) {
                layer.msg('获取任务信息失败，请重试', {icon: 2});
                return;
            }

            var taskData = res.data;
            var hasExtractPath = taskData.EXTRACT_PATH && taskData.EXTRACT_PATH.trim() !== '';
            var hasZipFile = taskData.ZIP_FILE_PATH && taskData.ZIP_FILE_PATH.trim() !== '';

            // 只检查是否上传了全景图ZIP文件
            if (!hasZipFile) {
                layer.msg('该任务尚未上传全景图ZIP文件，请先上传全景图压缩包', {
                    icon: 2,
                    time: 3000
                });
                return;
            }

            // 检查是否有解压路径（表示ZIP文件已被处理）
            if (!hasExtractPath) {
                layer.msg('全景图文件正在处理中，请稍后再试', {
                    icon: 2,
                    time: 3000
                });
                return;
            }

            // 条件满足，执行导出
            exportPanorama();

        }).fail(function() {
            layer.msg('网络请求失败，请检查网络连接', {icon: 2});
        });
    }

    /**
     * 加载节点列表
     */
    function loadNodesList() {
        if (!currentTaskId) return;

        $.get('/panorama/nodes/list', {taskId: currentTaskId}, function(res) {
            if (res.success && res.data) {
                nodesList = res.data;
                updateNodeSelector();

                // 显示节点选择器容器
                $('#nodeSelectorContainer').show();
            } else {
                // 如果获取节点列表失败，隐藏节点选择器
                $('#nodeSelectorContainer').hide();
                console.warn('获取节点列表失败:', res.msg);
            }
        }).fail(function() {
            $('#nodeSelectorContainer').hide();
            console.warn('获取节点列表网络请求失败');
        });
    }

    /**
     * 更新节点选择器
     */
    function updateNodeSelector() {
        var options = '<option value="">请选择节点</option>';

        $.each(nodesList, function(i, node) {
            options += '<option value="' + node.id + '">' + node.title + '</option>';
        });

        $('#nodeSelector').html(options);
        form.render('select');
    }

    /**
     * 切换到指定节点
     */
    function switchToNode(nodeId) {
        if (!nodeId || nodeId === currentNodeId) return;

        console.log('开始切换到节点:', nodeId);

        // 先更新当前节点ID和UI
        currentNodeId = nodeId;

        // 刷新热点表格
        refreshHotspotTableForNode(nodeId);

        // 更新节点指示器
        updateNodeIndicator('switch');

        // 通过postMessage通知预览窗口切换节点
        var panoramaFrame = document.getElementById('panoramaFrame');
        if (panoramaFrame && panoramaFrame.contentWindow) {
            try {
                console.log('发送节点切换消息到iframe');
                panoramaFrame.contentWindow.postMessage({
                    type: 'switchNode',
                    nodeId: nodeId
                }, '*');

                layer.msg('正在切换到节点: ' + nodeId, {icon: 1, time: 2000});
            } catch (error) {
                console.error('发送节点切换消息失败:', error);
                layer.msg('发送节点切换指令失败', {icon: 2});
            }
        } else {
            console.warn('预览窗口不可用');
            layer.msg('预览窗口不可用，无法切换节点', {icon: 2});
        }
    }

    /**
     * 清空任务信息
     */
    function clearTaskInfo() {
        currentTaskId = null;
        currentNodeId = null;
        nodesList = [];

        $('#taskName').text('未选择任务');
        $('#modelInfo').text('-');
        $('#taskDescription').text('-');
        $('#createTime').text('-');
        $('#taskStatus').text('-');
        $('#zipStatus').text('未上传');
        $('#excelStatus').text('未上传');

        // 隐藏节点选择器
        $('#nodeSelectorContainer').hide();
        $('#nodeSelector').html('<option value="">请选择节点</option>');
        form.render('select');

        disableUploadButtons();

        // 使用统一的导出按钮管理函数
        updateExportButtonState();

        // 清空表格
        if (hotspotTable) {
            hotspotTable.reload({
                data: []
            });
        }

        // 清空预览
        hidePreview();

        // 显示任务选择蒙版
        showTaskSelectionMask();
    }

    /**
     * 启用上传按钮和查看按钮
     */
    function enableUploadButtons() {
        $('#uploadZipBtn').prop('disabled', false);
        $('#viewDeviceBtn').prop('disabled', false);

        // 更新设备状态显示
        if (typeof updateDeviceStatus === 'function') {
            updateDeviceStatus();
        }
    }

    /**
     * 禁用上传按钮和查看按钮
     */
    function disableUploadButtons() {
        $('#uploadZipBtn').prop('disabled', true);
        $('#viewDeviceBtn').prop('disabled', true);

        // 重置设备状态显示
        $('#deviceStatus').text('-');
    }

    /**
     * 初始化热点表格
     */
    function initHotspotTable() {
        hotspotTable = table.render({
            elem: '#hotspotTable',
            url: '/panorama/hotspot/list',
            where: {
                taskId: 0 // 初始为0，不加载数据
            },
            page: true,
            limit: 25, // 增加每页显示行数以显示更多内容
            limits: [25, 50, 100],
            height: 'full-45', // 自适应高度，减去头部高度
            cols: [[
                {type: 'numbers', title: '序号', width: 60}, // 改为序号列，缩小宽度
                {field: 'TITLE_DISPLAY', title: '热点标题', width: 200, templet: '#titleDisplayTpl'},
                {field: 'DESCRIPTION_DISPLAY', title: '热点描述', width: 280, templet: '#descriptionDisplayTpl'},
                {field: 'IS_EDITED', title: '状态', width: 70, templet: '#editStatusTpl'}, // 缩小状态列宽度
                {title: '操作', width: 100, toolbar: '#hotspotTableBar', fixed: 'right'} // 缩小操作列宽度
            ]],
            done: function() {
                // 表格渲染完成回调
            }
        });

        // 监听表格工具条
        table.on('tool(hotspotTable)', function(obj) {
            var data = obj.data;
            if (obj.event === 'edit') {
                editHotspot(data);
            } else if (obj.event === 'locate') {
                locateHotspot(data.HOTSPOT_ID);
            }
        });

        // 由于现在使用模板显示，不再支持直接单元格编辑
        // 编辑功能通过操作列的"编辑"按钮实现
    }

    /**
     * 加载热点数据
     */
    function loadHotspotData() {
        if (!currentTaskId) return;

        // 如果有当前节点ID，按节点过滤；否则加载所有热点
        var whereCondition = {
            taskId: currentTaskId
        };

        if (currentNodeId) {
            whereCondition.panoramaId = currentNodeId;
        }

        hotspotTable.reload({
            where: whereCondition
        });
    }

    /**
     * 初始化拖拽调整功能
     */
    function initResizeHandle() {
        var resizeHandle = document.getElementById('resizeHandle');
        var leftPanel = document.getElementById('leftPanel');
        var rightPanel = document.getElementById('rightPanel');
        var container = document.querySelector('.container-content');
        var iframe = document.getElementById('panoramaFrame');

        var isResizing = false;
        var startX = 0;
        var startLeftWidth = 0;

        resizeHandle.addEventListener('mousedown', function(e) {
            isResizing = true;
            startX = e.clientX;
            startLeftWidth = leftPanel.offsetWidth;

            // 添加全局样式，防止选择文本
            document.body.style.userSelect = 'none';
            document.body.style.cursor = 'col-resize';

            // 禁用iframe的鼠标事件，防止干扰拖拽
            if (iframe) {
                iframe.style.pointerEvents = 'none';
            }

            // 添加遮罩层防止iframe捕获事件
            createDragOverlay();

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mousemove', function(e) {
            if (!isResizing) return;

            var deltaX = e.clientX - startX;
            var newLeftWidth = startLeftWidth + deltaX;
            var containerWidth = container.offsetWidth;
            var minLeftWidth = 300;
            var maxLeftWidth = containerWidth * 0.6;
            var minRightWidth = 300;

            // 限制最小和最大宽度
            if (newLeftWidth < minLeftWidth) {
                newLeftWidth = minLeftWidth;
            } else if (newLeftWidth > maxLeftWidth) {
                newLeftWidth = maxLeftWidth;
            } else if (containerWidth - newLeftWidth - 6 < minRightWidth) {
                newLeftWidth = containerWidth - minRightWidth - 6;
            }

            // 设置新宽度
            leftPanel.style.width = newLeftWidth + 'px';

            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('mouseup', function(e) {
            if (isResizing) {
                isResizing = false;

                // 恢复样式
                document.body.style.userSelect = '';
                document.body.style.cursor = '';

                // 恢复iframe的鼠标事件
                if (iframe) {
                    iframe.style.pointerEvents = '';
                }

                // 移除遮罩层
                removeDragOverlay();

                // 重新渲染表格以适应新宽度
                if (hotspotTable) {
                    setTimeout(function() {
                        hotspotTable.resize();
                    }, 100);
                }

                e.preventDefault();
                e.stopPropagation();
            }
        });

        // 窗口大小改变时重新计算
        window.addEventListener('resize', function() {
            var containerWidth = container.offsetWidth;
            var currentLeftWidth = leftPanel.offsetWidth;
            var minRightWidth = 300;

            if (containerWidth - currentLeftWidth - 6 < minRightWidth) {
                leftPanel.style.width = (containerWidth - minRightWidth - 6) + 'px';

                // 重新渲染表格
                if (hotspotTable) {
                    setTimeout(function() {
                        hotspotTable.resize();
                    }, 100);
                }
            }
        });
    }

    /**
     * 创建拖拽遮罩层
     */
    function createDragOverlay() {
        var overlay = document.createElement('div');
        overlay.id = 'dragOverlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            cursor: col-resize;
            background: transparent;
        `;
        document.body.appendChild(overlay);
    }

    /**
     * 移除拖拽遮罩层
     */
    function removeDragOverlay() {
        var overlay = document.getElementById('dragOverlay');
        if (overlay) {
            document.body.removeChild(overlay);
        }
    }

    /**
     * 初始化文件上传
     */
    function initFileUpload() {
        // ZIP文件上传
        var zipUploadInst = upload.render({
            elem: '#uploadZipBtn',
            url: '/panorama/upload/zip',
            accept: 'file',
            exts: 'zip',
            data: {
                taskId: function() {
                    return currentTaskId;
                }
            },
            before: function(obj) {
                if (!currentTaskId) {
                    layer.msg('请先选择任务', {icon: 2});
                    return false;
                }

                // 检查是否已存在热点数据
                checkExistingHotspotsBeforeUpload(obj, zipUploadInst);
                return false; // 阻止默认上传，由检查结果决定是否继续
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg('ZIP文件上传成功', {icon: 1});
                    $('#zipStatus').text('已上传');
                    loadHotspotData();
                    loadPreview();
                } else {
                    layer.msg('上传失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败', {icon: 2});
            }
        });

        // 查看设备数据按钮事件
        $('#viewDeviceBtn').click(function() {
            if (!currentTaskId) {
                layer.msg('请先选择任务', {icon: 2});
                return;
            }
            showDeviceListDialog();
        });
    }

    /**
     * 显示设备列表弹窗
     */
    function showDeviceListDialog() {

        var dialogHtml = '<div id="deviceListContainer" style="padding: 0;">' +
            '<div style="padding: 15px; border-bottom: 1px solid #e6e8eb; background: #fafbfc;">' +
            '<div style="display: flex; justify-content: space-between; align-items: center;">' +
            '<h3 style="margin: 0; font-size: 16px; color: #333;">' +
            '<i class="layui-icon layui-icon-table" style="color: #667eea;"></i> 单机数据管理</h3>' +
            '<div>' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" id="uploadExcelInDialog" style="margin-right: 8px;">' +
            '<i class="layui-icon layui-icon-upload"></i> 上传Excel</button>' +
            '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" id="downloadExcelInDialog">' +
            '<i class="layui-icon layui-icon-download-circle"></i> 下载Excel</button>' +
            '</div>' +
            '</div></div>' +
            '<div style="height: 639px; overflow: hidden;">' +
            '<table class="layui-hide" id="deviceTable" lay-filter="deviceTable"></table>' +
            '</div></div>';

        layer.open({
            type: 1,
            title: false,
            content: dialogHtml,
            area: ['900px', '700px'],
            success: function(layero, index) {
                // 初始化设备表格
                initDeviceTable();

                // 初始化弹窗内的上传功能
                initDialogUpload();

                // 初始化弹窗内的下载功能
                initDialogDownload();
            }
        });
    }

    /**
     * 初始化设备表格
     */
    function initDeviceTable() {
        table.render({
            elem: '#deviceTable',
            url: '/panorama/device/list',
            where: {
                taskId: currentTaskId
            },
            page: true,
            limit: 15,
            limits: [15, 30, 50],
            height: 639,
            cols: [[
                {type: 'numbers', title: '序号', width: 50},
                {field: 'DEVICE_NAME', title: '单机名称'},
                {field: 'DEVICE_CODE', title: '单机代号', width: 120},
                {field: 'BATCH_NO', title: '批次号', width: 100},
                {field: 'MODEL_ID', title: '型号ID', width: 100},
                {field: 'MODEL_NAME', title: '型号名称', width: 120}
            ]],
            done: function() {
                // 表格渲染完成回调
            }
        });
    }

    /**
     * 初始化弹窗内的上传功能
     */
    function initDialogUpload() {
        upload.render({
            elem: '#uploadExcelInDialog',
            url: '/panorama/upload/excel',
            accept: 'file',
            exts: 'xlsx|xls',
            data: {
                taskId: function() {
                    return currentTaskId;
                }
            },
            before: function(obj) {
                layer.load();
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.success) {
                    layer.msg('Excel文件上传成功', {icon: 1});
                    // 刷新设备表格
                    table.reload('deviceTable');
                    // 更新外部状态
                    updateDeviceStatus();
                } else {
                    layer.msg('上传失败: ' + res.msg, {icon: 2});
                }
            },
            error: function() {
                layer.closeAll('loading');
                layer.msg('上传失败', {icon: 2});
            }
        });
    }

    /**
     * 初始化弹窗内的下载功能
     */
    function initDialogDownload() {
        $('#downloadExcelInDialog').click(function() {
            if (!currentTaskId) {
                layer.msg('请先选择任务', {icon: 2});
                return;
            }

            // 显示下载提示
            layer.msg('正在生成Excel文件...', {icon: 16, time: 2000});

            // 创建隐藏的iframe进行下载
            var downloadUrl = '/panorama/device/export?taskId=' + currentTaskId;
            var iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = downloadUrl;
            document.body.appendChild(iframe);

            // 下载完成后移除iframe
            setTimeout(function() {
                document.body.removeChild(iframe);
            }, 5000);
        });
    }

    /**
     * 更新设备状态显示
     */
    function updateDeviceStatus() {
        $.get('/panorama/device/list', {taskId: currentTaskId, page: 1, limit: 1}, function(res) {
            if (res.code === 0 && res.count > 0) {
                $('#deviceStatus').text('共' + res.count + '条');
            } else {
                $('#deviceStatus').text('-');
            }
        });
    }

    /**
     * 隐藏顶部操作按钮
     */
    function hideTopActionButtons() {
        $('#createTaskBtn').hide();
        // 导出按钮的显示/隐藏由updateExportButtonState统一管理
    }

    /**
     * 显示顶部操作按钮
     */
    function showTopActionButtons() {
        $('#createTaskBtn').show();
        // 导出按钮的显示/隐藏和状态由updateExportButtonState统一管理
        updateExportButtonState();
    }

    /**
     * 显示任务选择蒙版
     */
    function showTaskSelectionMask() {
        $('#taskSelectionMask').show();

        // 隐藏顶部操作按钮
        hideTopActionButtons();

        // 绑定蒙版上的创建任务按钮事件
        $('#maskCreateTaskBtn').off('click').on('click', function() {
            showCreateTaskDialog();
        });
    }

    /**
     * 隐藏任务选择蒙版
     */
    function hideTaskSelectionMask() {
        $('#taskSelectionMask').addClass('fade-out');

        // 显示顶部操作按钮
        showTopActionButtons();

        setTimeout(function() {
            $('#taskSelectionMask').hide().removeClass('fade-out');
        }, 300);
    }

    /**
     * 显示创建任务对话框
     */
    function showCreateTaskDialog() {
        // 检查layer是否可用
        if (typeof layer === 'undefined') {
            alert('Layer组件未加载，请检查Layui是否正确引入');
            return;
        }



        // 尝试直接使用HTML内容而不是jQuery对象
        var dialogHtml = $('#createTaskDialog').html();

        layer.open({
            type: 1,
            title: '创建新任务',
            content: dialogHtml,
            area: ['500px', '400px'],
            btn: ['创建', '取消'],
            yes: function(index, layero) {

                // 手动获取表单数据并提交
                var formData = {
                    taskName: layero.find('input[name="taskName"]').val(),
                    modelId: layero.find('input[name="modelId"]').val(),
                    modelName: layero.find('input[name="modelName"]').val(),
                    description: layero.find('textarea[name="description"]').val()
                };

                // 验证必填字段
                if (!formData.taskName) {
                    layer.msg('请输入任务名称', {icon: 2});
                    return false;
                }

                createTask(formData);
                return false; // 阻止默认关闭
            },
            success: function(layero, index) {
                // 重新渲染表单
                form.render();
            }
        });
    }

    /**
     * 创建任务
     */
    function createTask(formData) {
        $.post('/panorama/task/create', formData, function(res) {
            if (res.success) {
                layer.closeAll();
                layer.msg('任务创建成功', {icon: 1});

                // 先加载任务列表，然后自动选择新创建的任务
                loadTaskList(function() {
                    // 任务列表加载完成后，自动选择新创建的任务
                    setTimeout(function() {
                        $('#taskSelect').val(res.data.taskId);
                        form.render('select');
                        selectTask(res.data.taskId);
                    }, 200);
                });
            } else {
                layer.msg('创建失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 编辑热点
     */
    function editHotspot(hotspotData) {

        // 使用HTML内容而不是jQuery对象，参照createTaskDialog的实现
        var dialogHtml = $('#editHotspotDialog').html();

        layer.open({
            type: 1,
            title: '编辑热点信息',
            content: dialogHtml,
            area: ['500px', '300px'],
            btn: ['保存', '取消'],
            yes: function(index, layero) {

                // 获取选中的设备ID和设备名称
                var selectedDeviceId = layero.find('select[name="editedTitle"]').val();
                var selectedDeviceName = '';

                if (selectedDeviceId && deviceList) {
                    var selectedDevice = deviceList.find(function(device) {
                        return device.DEVICE_ID == selectedDeviceId;
                    });
                    if (selectedDevice) {
                        selectedDeviceName = selectedDevice.DEVICE_NAME;
                    }
                }

                // 手动获取表单数据并提交
                var formData = {
                    hotspotId: hotspotData.HOTSPOT_ID,
                    editedTitle: selectedDeviceName, // 使用设备名称而不是设备ID
                    editedDescription: layero.find('input[name="editedDescription"]').val(),
                    deviceId: selectedDeviceId // 保留设备ID用于关联
                };

                // 验证必填字段
                if (!selectedDeviceId) {
                    layer.msg('请选择热点标题', {icon: 2});
                    return false;
                }

                updateHotspot(formData);
                return false; // 阻止默认关闭
            },
            success: function(layero, index) {

                // 监听热点标题选择变化 - 先绑定事件
                form.on('select(hotspotTitleSelect)', function(data) {
                    var deviceName = data.elem[data.elem.selectedIndex].text;
                    var deviceId = data.value;

                    // 根据选择的单机自动填充描述
                    if (deviceId && deviceList) {
                        var selectedDevice = deviceList.find(function(device) {
                            return device.DEVICE_ID == deviceId;
                        });

                        if (selectedDevice) {
                            var description = selectedDevice.DEVICE_NAME + ' ' +
                                            (selectedDevice.DEVICE_CODE || '') + ' ' +
                                            (selectedDevice.BATCH_NO || '');
                            layero.find('input[name="editedDescription"]').val(description.trim());
                        }
                    }
                });

                // 加载单机列表到热点标题select，并在完成后渲染
                loadDeviceListForHotspot(layero, hotspotData, function() {
                    // 在数据加载完成后进行渲染
                    form.render(layero.find('.layui-form select'));

                    // 鼠标滑动 layer 内部滚动条时移除下拉框，以规避错位
                    var selectElem = layero.find('.layui-form-select');
                    layero.find('.layui-layer-content').on('scroll', function() {
                        selectElem.removeClass('layui-form-selected');
                        layui.$('.layui-select-panel-wrap').detach();
                    });
                });
            }
        });
    }

    /**
     * 更新热点信息
     */
    function updateHotspot(formData) {
        $.post('/panorama/hotspot/update', formData, function(res) {
            if (res.success) {
                layer.closeAll();
                layer.msg('热点信息更新成功', {icon: 1});
                hotspotTable.reload();
                // 移除updatePreview()调用，避免刷新全景图导致用户忘记编辑的热点位置
            } else {
                layer.msg('更新失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }



    /**
     * 加载单机信息列表
     */
    function loadDeviceList() {
        if (!currentTaskId) return;

        $.get('/panorama/device/list', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                deviceList = res.data;
                // 移除updateDeviceSelect调用，因为不再需要关联单机select
            }
        });
    }

    /**
     * 为热点编辑对话框加载单机列表
     */
    function loadDeviceListForHotspot(layero, hotspotData, callback) {

        if (!currentTaskId) {
            if (typeof callback === 'function') {
                callback();
            }
            return;
        }

        // 强制重新加载设备数据，确保数据最新
        $.get('/panorama/device/list', {taskId: currentTaskId}, function(res) {
            if (res.code==0) {
                deviceList = res.data;
                populateHotspotTitleSelect(layero, hotspotData);

                // 数据填充完成后执行回调
                if (typeof callback === 'function') {
                    callback();
                }
            } else {
                if (typeof callback === 'function') {
                    callback();
                }
            }
        }).fail(function(status, error) {
            if (typeof callback === 'function') {
                callback();
            }
        });
    }

    /**
     * 填充热点标题select选项
     */
    function populateHotspotTitleSelect(layero, hotspotData) {

        var $select = layero.find('select[name="editedTitle"]');

        // 参照任务select的实现方式，使用html()而不是append()
        var options = '<option value="">请选择单机名称</option>';

        if (deviceList && deviceList.length > 0) {
            for (var i = 0; i < deviceList.length; i++) {
                var device = deviceList[i];
                options += '<option value="' + device.DEVICE_ID + '">' + device.DEVICE_NAME + '</option>';
            }
        }

        $select.html(options);

        // 设置当前选中值（如果有关联的设备）
        if (hotspotData.DEVICE_ID) {
            $select.val(hotspotData.DEVICE_ID);
        } else if (hotspotData.EDITED_TITLE) {
            // 如果没有关联设备但有编辑标题，尝试匹配单机名称
            var matchedDevice = deviceList.find(function(device) {
                return device.DEVICE_NAME === hotspotData.EDITED_TITLE;
            });
            if (matchedDevice) {
                $select.val(matchedDevice.DEVICE_ID);
            }
        }

        // 设置描述字段的值
        var currentDescription = hotspotData.EDITED_DESCRIPTION || hotspotData.ORIGINAL_DESCRIPTION || '';
        layero.find('input[name="editedDescription"]').val(currentDescription);
    }

    // updateDeviceSelect函数已移除，因为不再需要关联单机select

    /**
     * 加载预览
     */
    function loadPreview() {
        if (!currentTaskId) return;

        $.get('/panorama/preview/path', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                showPreview(res.data.previewUrl);
            } else {
                hidePreview();
            }
        });
    }

    /**
     * 显示预览
     */
    function showPreview(previewUrl) {
        $('#previewContainer .preview-placeholder').hide();
        $('#panoramaFrame').attr('src', previewUrl).show();
    }

    /**
     * 隐藏预览
     */
    function hidePreview() {
        $('#panoramaFrame').hide();
        $('#previewContainer .preview-placeholder').show();
    }

    /**
     * 更新预览
     */
    function updatePreview() {
        // 刷新iframe
        var iframe = document.getElementById('panoramaFrame');
        if (iframe && iframe.src) {
            iframe.src = iframe.src;
        }
    }

    /**
     * 热点定位功能
     * @param {Number} hotspotId 热点ID
     */
    function locateHotspot(hotspotId) {
        if (!hotspotId) {
            layer.msg('热点ID无效', {icon: 2});
            return;
        }

        // 检查iframe是否已加载
        var iframe = document.getElementById('panoramaFrame');
        if (!iframe || !iframe.src || iframe.style.display === 'none') {
            layer.msg('请先上传全景图文件', {icon: 2});
            return;
        }

        // 调用后端API获取热点位置信息
        $.ajax({
            url: '/panorama/hotspot/locate',
            type: 'POST',
            data: {
                hotspotId: hotspotId
            },
            success: function(res) {
                if (res.success && res.data) {
                    var pan = parseFloat(res.data.PAN);
                    var tilt = parseFloat(res.data.TILT);

                    // 验证数据有效性
                    if (isNaN(pan) || isNaN(tilt)) {
                        layer.msg('热点位置数据无效', {icon: 2});
                        return;
                    }

                    // 向iframe发送定位消息
                    var message = {
                        type: 'locateHotspot',
                        pan: pan,
                        tilt: tilt,
                        speed: 1.5 // 快速过渡效果
                    };

                    try {
                        iframe.contentWindow.postMessage(message, '*');
                    } catch (e) {
                        layer.msg('定位失败，请确保全景图已完全加载', {icon: 2});
                    }
                } else {
                    layer.msg('获取热点位置失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('定位请求失败，请重试', {icon: 2});
            }
        });
    }

    /**
     * 导出全景图包
     */
    function exportPanorama() {
        layer.confirm('确定要导出当前任务的全景图包吗？', {
            icon: 3,
            title: '确认导出'
        }, function(index) {
            layer.close(index);

            var loading = layer.msg('正在导出全景图包，请稍候...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            // 先尝试使用$.fileDownload
            if (typeof $.fileDownload === 'function') {
                $.fileDownload('/panorama/export', {
                    httpMethod: 'POST',
                    data: {
                        taskId: currentTaskId
                    },
                    prepareCallback: function(url) {
                    },
                    successCallback: function(url) {
                        layer.close(loading);
                        layer.msg('导出成功', {icon: 1});

                        // 刷新任务信息，更新状态
                        if (currentTaskId) {
                            updateExportButtonState();
                        }
                    },
                    failCallback: function(html, url) {
                        layer.close(loading);
                        layer.msg('导出失败', {icon: 2});
                    }
                });
            } else {
                // 备用方案：使用表单提交
                var $form = $('<form>', {
                    method: 'POST',
                    action: '/panorama/export',
                    style: 'display: none;'
                });

                $form.append($('<input>', {
                    type: 'hidden',
                    name: 'taskId',
                    value: currentTaskId
                }));

                $('body').append($form);
                $form.submit();
                $form.remove();

                // 延迟关闭loading
                setTimeout(function() {
                    layer.close(loading);
                    layer.msg('导出请求已发送', {icon: 1});
                    updateExportButtonState();
                }, 2000);
            }
        });
    }

    /**
     * 检查是否已存在热点数据（上传前）
     */
    function checkExistingHotspotsBeforeUpload(uploadObj, uploadInstance) {
        $.get('/panorama/check/hotspots', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                if (res.hasData) {
                    // 存在热点数据，显示确认对话框
                    layer.confirm('当前任务已存在热点数据，重新上传将清除所有已编辑的热点信息，是否继续？', {
                        icon: 3,
                        title: '重新上传全景图',
                        btn: ['继续上传', '取消']
                    }, function(index) {
                        // 用户确认继续上传
                        layer.close(index);
                        clearTaskDataAndUpload(uploadObj, uploadInstance);
                    }, function(index) {
                        // 用户取消上传
                        layer.close(index);
                    });
                } else {
                    // 不存在热点数据，直接上传
                    proceedWithUpload(uploadObj, uploadInstance);
                }
            } else {
                layer.msg('检查热点数据失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 清理任务数据并上传
     */
    function clearTaskDataAndUpload(uploadObj, uploadInstance) {
        layer.load();
        $.post('/panorama/clear/data', {taskId: currentTaskId}, function(res) {
            if (res.success) {
                // 清理成功，继续上传
                proceedWithUpload(uploadObj, uploadInstance);
            } else {
                layer.closeAll('loading');
                layer.msg('清理数据失败: ' + res.msg, {icon: 2});
            }
        }).fail(function() {
            layer.closeAll('loading');
            layer.msg('网络请求失败', {icon: 2});
        });
    }

    /**
     * 执行文件上传
     */
    function proceedWithUpload(uploadObj, uploadInstance) {
        // 显示加载状态
        if (!$('.layui-layer-loading').length) {
            layer.load();
        }

        // 使用Layui upload实例的upload方法继续上传
        if (uploadInstance && typeof uploadInstance.upload === 'function') {
            // 临时修改upload配置，移除before回调以避免循环
            var originalBefore = uploadInstance.config.before;
            uploadInstance.config.before = function() {
                layer.load(); // 显示加载状态
                return true; // 允许上传
            };

            // 重新触发上传
            uploadInstance.upload();

            // 恢复原始before回调
            setTimeout(function() {
                uploadInstance.config.before = originalBefore;
            }, 100);
        } else {
            layer.closeAll('loading');
            layer.msg('上传实例无效，请重新选择文件', {icon: 2});
        }
    }

    // 全局变量：当前节点ID
    var currentNodeId = null;

    // 全局变量：节点列表
    var nodesList = [];

    // 全局变量：节点选择器是否正在同步（防止循环触发）
    var isNodeSelectorSyncing = false;

    // 监听iframe消息
    window.addEventListener('message', function(event) {
        // 处理来自全景图iframe的消息
        if (event.data && event.data.type) {
            switch(event.data.type) {
                case 'hotspotClick':
                    // 热点被点击
                    break;
                case 'viewChange':
                    // 视角改变
                    break;
                case 'hotspotLocationComplete':
                    // 热点定位完成，只在失败时显示错误信息
                    if (!event.data.success) {
                        layer.msg('热点定位失败', {icon: 2});
                    }
                    break;
                case 'nodeSwitch':
                    // 节点切换事件
                    handleNodeSwitch(event.data);
                    break;
                case 'nodeSwitchError':
                    // 节点切换错误
                    handleNodeSwitchError(event.data);
                    break;
            }
        }
    });

    /**
     * 处理节点切换事件
     * @param {Object} data 节点切换数据
     */
    function handleNodeSwitch(data) {
        try {
            var newNodeId = data.nodeId;
            var switchType = data.switchType; // 'initial' 或 'switch'

            // 更新当前节点ID
            var previousNodeId = currentNodeId;
            currentNodeId = newNodeId;

            // 更新节点显示
            updateNodeDisplay(newNodeId, switchType);

            // 如果是节点切换（非初始化），刷新热点表格
            if (switchType === 'switch' && currentTaskId) {
                refreshHotspotTableForNode(newNodeId);

                // 显示节点切换提示
                layer.msg('已切换到节点: ' + newNodeId, {
                    icon: 1,
                    time: 2000
                });
            } else if (switchType === 'initial' && currentTaskId) {
                refreshHotspotTableForNode(newNodeId);
            }

        } catch (error) {
            // 处理节点切换事件失败
        }
    }

    /**
     * 更新节点显示
     * @param {String} nodeId 节点ID
     * @param {String} switchType 切换类型
     */
    function updateNodeDisplay(nodeId, switchType) {
        try {
            if (nodeId && nodeId !== '-') {
                // 显示节点选择器容器
                $('#nodeSelectorContainer').show();

                // 同步更新节点选择器的选中值
                isNodeSelectorSyncing = true;
                $('#nodeSelector').val(nodeId);
                form.render('select');
                isNodeSelectorSyncing = false;

                // 更新节点指示器
                updateNodeIndicator(switchType);
            } else {
                // 隐藏节点选择器容器
                $('#nodeSelectorContainer').hide();
            }

        } catch (error) {
            console.error('更新节点显示失败:', error);
        }
    }

    /**
     * 更新节点指示器
     * @param {String} switchType 切换类型
     */
    function updateNodeIndicator(switchType) {
        var nodeIndicator = $('#nodeIndicator');

        if (switchType === 'switch') {
            // 节点切换时的闪烁效果
            nodeIndicator.find('i').css('color', '#FF5722');
            setTimeout(function() {
                nodeIndicator.find('i').css('color', '#5FB878');
            }, 1000);
        } else {
            // 初始化时的正常颜色
            nodeIndicator.find('i').css('color', '#5FB878');
        }
    }

    /**
     * 处理节点切换错误
     * @param {Object} data 错误数据
     */
    function handleNodeSwitchError(data) {
        console.error('节点切换失败:', data);
        layer.msg('节点切换失败: ' + (data.error || '未知错误'), {
            icon: 2,
            time: 3000
        });

        // 恢复节点选择器到之前的状态
        if (currentNodeId) {
            isNodeSelectorSyncing = true;
            $('#nodeSelector').val(currentNodeId);
            form.render('select');
            isNodeSelectorSyncing = false;
        }
    }

    /**
     * 为指定节点刷新热点表格
     * @param {String} nodeId 节点ID
     */
    function refreshHotspotTableForNode(nodeId) {
        if (!currentTaskId || !hotspotTable) {
            return;
        }

        try {

            // 显示加载状态
            var loadingIndex = layer.load(2, {
                shade: [0.1, '#fff'],
                content: '正在加载节点热点...',
                success: function(layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '40px',
                        'width': '200px'
                    });
                }
            });

            // 重新加载表格数据，包含panoramaId过滤参数
            hotspotTable.reload({
                where: {
                    taskId: currentTaskId,
                    panoramaId: nodeId
                },
                page: {
                    curr: 1 // 重置到第一页，因为不同节点的热点数量可能不同
                },
                done: function(res, curr, count) {
                    // 关闭加载状态
                    layer.close(loadingIndex);

                    // 显示加载结果
                    if (res && res.code === 0) {
                        var hotspotCount = res.count || 0;

                        // 如果没有热点，显示提示
                        if (hotspotCount === 0) {
                            layer.msg('当前节点暂无热点数据', {
                                icon: 0,
                                time: 2000
                            });
                        }
                    } else {
                        layer.msg('加载热点数据失败', {icon: 2});
                    }
                }
            });

        } catch (error) {
            layer.msg('刷新热点表格失败', {icon: 2});
        }
    }
});
